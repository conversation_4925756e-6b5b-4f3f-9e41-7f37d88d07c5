2"""
整合的IQ数据分析和地图生成器
功能：
1. 读取WAV文件并提取IQ数据
2. 生成时频图（短时傅里叶变换）
3. 提取GNSS坐标信息
4. 生成高德地图并在坐标点旁显示时频图
"""

import requests
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
import os
import struct
import msgpack
from datetime import datetime, timezone, timedelta
from scipy import signal
from WGS84_GCJ02 import wgs84_to_gcj02
from htra_api import *

# 配置matplotlib字体以避免中文字体警告
def configure_matplotlib_font():
    """配置matplotlib字体设置"""
    try:
        # 尝试设置中文字体
        import matplotlib.font_manager as fm

        # 查找系统中可用的中文字体
        chinese_fonts = []
        for font in fm.fontManager.ttflist:
            if 'SimHei' in font.name or 'Microsoft YaHei' in font.name or 'SimSun' in font.name:
                chinese_fonts.append(font.name)

        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            print(f"已配置中文字体: {chinese_fonts[0]}")
        else:
            # 如果没有中文字体，使用英文标签
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            print("未找到中文字体，将使用英文标签")

    except Exception as e:
        print(f"字体配置警告: {e}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

# 初始化字体配置
configure_matplotlib_font()

# 高德地图API配置
AMAP_URL = "https://restapi.amap.com/v3/staticmap"
AMAP_API_KEY = "fd2adfc70d86343b1181195c0fc9a0ee"

class IntegratedIQMapAnalyzer:
    def __init__(self, wav_file_path):
        self.wav_file_path = wav_file_path
        self.gnss_data = None
        self.iq_data = None
        self.sample_rate = None
        self.center_freq = None
        
    def extract_gnss_info(self):
        """提取GNSS信息"""
        try:
            # 读取文件信息
            file_info = self.get_iqs_wav_file_info()
            if not file_info:
                print("Failed to get file information")
                return None

            gnss_data = []
            # 读取每个数据包的GNSS信息
            for packet_num in range(min(file_info['PacketCount'], 5)):  # 限制读取前5个包
                packet_data = self.get_iqs_wav_file_data(file_info['IQS_Profile'], packet_num)
                if not packet_data:
                    continue

                gnss_info = {
                    'PacketNumber': packet_num,
                    'Latitude': packet_data['Latitude'],
                    'Longitude': packet_data['Longitude'],
                    'AbsoluteTimeStamp': packet_data['AbsoluteTimeStamp'],
                    'IQData': packet_data['IQData']
                }
                gnss_data.append(gnss_info)

            self.gnss_data = gnss_data
            return gnss_data
        except Exception as e:
            print(f"提取GNSS信息时出错: {e}")
            return None

    def get_iqs_wav_file_info(self):
        """获取WAV文件信息"""
        try:
            with open(self.wav_file_path, 'rb') as file:
                # 读取IQS_Profile和IQS_StreamInfo结构体大小
                file.seek(108)
                struct_size_bytes = file.read(2)
                struct_size = self.swap_16(struct.unpack('<H', struct_size_bytes)[0])
                
                # 读取IQS_Profile和IQS_StreamInfo数据
                iqs_profile_stream_data = file.read(struct_size)
                
                # 使用msgpack解析数据
                unpacker = msgpack.Unpacker()
                unpacker.feed(iqs_profile_stream_data)
                unpacked_data = list(unpacker)
                
                # 创建IQS_Profile结构体
                iqs_profile = IQS_Profile_TypeDef()
                
                # 解析关键参数
                center_freq = unpacked_data[0] if len(unpacked_data) > 0 else 2450000000.0
                sample_rate = unpacked_data[38] if len(unpacked_data) > 38 else 10000000.0
                
                # 如果采样率过小，可能需要转换单位
                if sample_rate < 1.0:
                    sample_rate = sample_rate * 1000000
                
                self.center_freq = center_freq
                self.sample_rate = sample_rate
                
                # 读取IQ数据总长度
                file.seek(25 * 1024 * 1024 + 404)
                iq_size_bytes = file.read(4)
                iq_size = struct.unpack('<I', iq_size_bytes)[0]
                
                # 计算数据包数量
                packet_count = iq_size // 64968
                
                return {
                    'IQS_Profile': iqs_profile,
                    'PacketCount': packet_count,
                    'IQSize': iq_size,
                    'CenterFreq_Hz': center_freq,
                    'IQSampleRate': sample_rate
                }
        except Exception as e:
            print(f"读取文件信息时出错: {e}")
            return None

    def get_iqs_wav_file_data(self, iqs_profile, packet_num):
        """获取指定数据包的IQ数据"""
        try:
            with open(self.wav_file_path, 'rb') as file:
                # 读取GNSS坐标
                file.seek(408 + packet_num * 405 + 2 + 8 + 2 + 2 + 25 * 4 + 25 * 8 + 25 * 1 + 2 + 2 + 2 + 2 + 8)
                
                # 读取Latitude (纬度)
                latitude_bytes = file.read(4)
                latitude = self.convert_endian_float(latitude_bytes)

                # 读取Longitude (经度)
                longitude_bytes = file.read(4)
                longitude = self.convert_endian_float(longitude_bytes)
                
                # 读取时间戳
                file.seek(408 + packet_num * 405 + 2 + 8 + 2 + 2 + 25 * 4 + 25 * 8 + 25 * 1 + 2 + 2 + 2 + 2)
                abs_timestamp_bytes = file.read(8)
                abs_timestamp = self.convert_endian_double(abs_timestamp_bytes)

                # 读取IQ数据
                file.seek(25 * 1024 * 1024 + 408 + packet_num * 64968)
                
                # 计算样本数（假设16位复数格式）
                samples = 64968 // 4  # 每个IQ对占用4字节
                
                iq_data = []
                for i in range(min(samples, 16000)):  # 限制读取数量以提高性能
                    q_bytes = file.read(2)
                    i_bytes = file.read(2)
                    if len(q_bytes) < 2 or len(i_bytes) < 2:
                        break
                    q_val = struct.unpack('<h', q_bytes)[0]
                    i_val = struct.unpack('<h', i_bytes)[0]
                    iq_data.append((i_val, q_val))  # 注意：这里改为(I, Q)顺序
                
                return {
                    'Latitude': latitude,
                    'Longitude': longitude,
                    'AbsoluteTimeStamp': abs_timestamp,
                    'IQData': iq_data
                }
        except Exception as e:
            print(f"读取数据包 {packet_num} 时出错: {e}")
            return None

    def generate_spectrogram(self, iq_data, title="Spectrogram"):
        """生成时频图"""
        try:
            # 转换IQ数据为复数信号
            i_data = [iq[0] for iq in iq_data]
            q_data = [iq[1] for iq in iq_data]
            complex_signal = np.array(i_data) + 1j * np.array(q_data)

            # 计算短时傅里叶变换
            f, t, Zxx = signal.stft(complex_signal,
                                   fs=self.sample_rate,
                                   window='hann',
                                   nperseg=256,
                                   noverlap=128)

            # 创建时频图
            fig, ax = plt.subplots(figsize=(6, 4))
            im = ax.pcolormesh(t * 1000, f / 1e6, 20 * np.log10(np.abs(Zxx)),
                              shading='gouraud', cmap='viridis')
            ax.set_ylabel('Frequency (MHz)')
            ax.set_xlabel('Time (ms)')
            ax.set_title(title)

            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Power (dB)')

            plt.tight_layout()

            # 保存为字节流
            buf = BytesIO()
            plt.savefig(buf, format='png', dpi=100, bbox_inches='tight')
            buf.seek(0)
            spectrogram_image = Image.open(buf)
            plt.close()

            return spectrogram_image
        except Exception as e:
            print(f"生成时频图时出错: {e}")
            return None

    # 辅助函数
    def swap_16(self, val):
        return ((val & 0x00ff) << 8) | ((val & 0xff00) >> 8)

    def swap_32(self, val):
        return ((val & 0xff000000) >> 24) | \
               ((val & 0x00ff0000) >> 8) | \
               ((val & 0x0000ff00) << 8) | \
               ((val & 0x000000ff) << 24)

    def swap_64(self, value):
        result = 0
        result |= (value & 0xff00000000000000) >> 56
        result |= (value & 0x00ff000000000000) >> 40
        result |= (value & 0x0000ff0000000000) >> 24
        result |= (value & 0x000000ff00000000) >> 8
        result |= (value & 0x00000000ff000000) << 8
        result |= (value & 0x0000000000ff0000) << 24
        result |= (value & 0x000000000000ff00) << 40
        result |= (value & 0x00000000000000ff) << 56
        return result

    def convert_endian_float(self, value_bytes):
        val_int = struct.unpack('<I', value_bytes)[0]
        val_int_swapped = self.swap_32(val_int)
        return struct.unpack('!f', struct.pack('!I', val_int_swapped))[0]

    def convert_endian_double(self, value_bytes):
        val_int = struct.unpack('<Q', value_bytes)[0]
        val_int_swapped = self.swap_64(val_int)
        return struct.unpack('!d', struct.pack('!Q', val_int_swapped))[0]

    def generate_map_with_spectrogram(self, output_filename="integrated_map.png"):
        """生成带有时频图的地图"""
        try:
            # 提取GNSS信息
            print("正在提取GNSS信息...")
            gnss_data = self.extract_gnss_info()

            if not gnss_data or len(gnss_data) == 0:
                print("未能提取到GNSS信息，使用默认坐标")
                # 使用默认坐标
                longitude, latitude = 112.99501037597656, 28.229507446289062
                # 生成默认IQ数据用于演示
                default_iq = [(np.random.randint(-1000, 1000), np.random.randint(-1000, 1000)) for _ in range(1000)]
                spectrogram_img = self.generate_spectrogram(default_iq, "Default Spectrogram")
            else:
                # 使用第一个有效的GNSS数据
                gnss_info = gnss_data[0]
                longitude = gnss_info['Longitude']
                latitude = gnss_info['Latitude']

                print(f"使用坐标: 经度={longitude:.6f}, 纬度={latitude:.6f}")

                # 生成时频图
                print("正在生成时频图...")
                spectrogram_img = self.generate_spectrogram(
                    gnss_info['IQData'],
                    f"Packet {gnss_info['PacketNumber']} Spectrogram"
                )

            # 转换坐标系
            gcj_lon, gcj_lat = wgs84_to_gcj02(longitude, latitude)
            print(f"转换后坐标 (GCJ-02): 经度={gcj_lon:.6f}, 纬度={gcj_lat:.6f}")

            # 获取地图
            print("正在获取地图...")
            map_image = self.get_amap_image(gcj_lon, gcj_lat)

            if map_image is None:
                print("获取地图失败")
                return False

            # 合成图像
            print("正在合成图像...")
            final_image = self.composite_map_and_spectrogram(map_image, spectrogram_img, gcj_lon, gcj_lat)

            # 保存结果
            final_image.save(output_filename)
            print(f"合成图像已保存到: {output_filename}")

            # 显示图像
            try:
                final_image.show()
                print("图像已在默认查看器中打开")
            except Exception as e:
                print(f"无法显示图像: {e}")

            return True

        except Exception as e:
            print(f"生成地图时出错: {e}")
            return False

    def get_amap_image(self, longitude, latitude, zoom=16, size=(1200, 800)):
        """获取高德地图图像"""
        try:
            params = {
                "location": f"{longitude},{latitude}",
                "zoom": zoom,
                "size": f"{size[0]}*{size[1]}",
                "scale": 2,
                "maptype": "satellite",
                "markers": f"mid,0xFF0000,A:{longitude},{latitude}",
                "key": AMAP_API_KEY
            }

            response = requests.get(AMAP_URL, params=params, timeout=30)

            if response.status_code == 200:
                return Image.open(BytesIO(response.content))
            else:
                print(f"获取地图失败，HTTP状态码: {response.status_code}")
                return None

        except Exception as e:
            print(f"获取地图时出错: {e}")
            return None

    def composite_map_and_spectrogram(self, map_image, spectrogram_image, longitude, latitude):
        """合成地图和时频图"""
        try:
            # 创建新的画布
            canvas_width = map_image.width + 50
            canvas_height = map_image.height + 50
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')

            # 粘贴地图
            canvas.paste(map_image, (25, 25))

            if spectrogram_image:
                # 调整时频图大小
                spec_width = min(300, map_image.width // 3)
                spec_height = min(200, map_image.height // 4)
                spectrogram_resized = spectrogram_image.resize((spec_width, spec_height), Image.Resampling.LANCZOS)

                # 计算时频图位置（右上角）
                spec_x = canvas_width - spec_width - 30
                spec_y = 30

                # 添加白色背景
                bg = Image.new('RGB', (spec_width + 10, spec_height + 10), 'white')
                canvas.paste(bg, (spec_x - 5, spec_y - 5))

                # 粘贴时频图
                canvas.paste(spectrogram_resized, (spec_x, spec_y))

            # 添加文字信息
            draw = ImageDraw.Draw(canvas)

            try:
                # 尝试使用系统字体
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                # 如果没有找到字体，使用默认字体
                font = ImageFont.load_default()

            # 添加坐标信息
            coord_text = f"坐标: {longitude:.6f}, {latitude:.6f}"
            draw.text((30, canvas_height - 40), coord_text, fill='black', font=font)

            # 添加文件信息
            file_text = f"文件: {os.path.basename(self.wav_file_path)}"
            draw.text((30, canvas_height - 20), file_text, fill='black', font=font)

            return canvas

        except Exception as e:
            print(f"合成图像时出错: {e}")
            return map_image  # 返回原始地图作为备用

def find_wav_files():
    """查找当前目录中的WAV文件"""
    wav_files = []
    for file in os.listdir('.'):
        if file.endswith('.wav'):
            wav_files.append(file)
    return sorted(wav_files)

def interactive_mode():
    """交互式模式"""
    print("=== 整合的IQ数据分析和地图生成器 ===")

    # 查找WAV文件
    wav_files = find_wav_files()

    if not wav_files:
        print("当前目录中没有找到WAV文件")
        return False

    print(f"找到 {len(wav_files)} 个WAV文件:")
    for i, file in enumerate(wav_files, 1):
        print(f"  {i}. {file}")

    # 让用户选择文件
    try:
        choice = input(f"\n请选择WAV文件 (1-{len(wav_files)}) 或按回车使用最新文件: ").strip()

        if choice == "":
            selected_file = wav_files[-1]
            print(f"使用最新文件: {selected_file}")
        else:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(wav_files):
                selected_file = wav_files[choice_idx]
                print(f"选择文件: {selected_file}")
            else:
                print("无效选择，使用最新文件")
                selected_file = wav_files[-1]
    except (ValueError, KeyboardInterrupt):
        print("使用最新文件")
        selected_file = wav_files[-1]

    # 创建分析器并运行
    analyzer = IntegratedIQMapAnalyzer(selected_file)
    output_filename = f"integrated_analysis_{os.path.splitext(selected_file)[0]}.png"

    success = analyzer.generate_map_with_spectrogram(output_filename)
    return success

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        wav_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else f"integrated_analysis_{os.path.splitext(os.path.basename(wav_file))[0]}.png"

        print(f"命令行模式: 文件={wav_file}, 输出={output_file}")

        if not os.path.exists(wav_file):
            print(f"文件不存在: {wav_file}")
            return False

        analyzer = IntegratedIQMapAnalyzer(wav_file)
        success = analyzer.generate_map_with_spectrogram(output_file)
    else:
        # 交互模式
        success = interactive_mode()

    if success:
        print("\n✅ 分析完成！")
        print("生成的图像包含:")
        print("  - 高德卫星地图")
        print("  - GNSS坐标标记")
        print("  - IQ数据时频图")
        print("  - 坐标和文件信息")
    else:
        print("\n❌ 分析失败！")

    return success

if __name__ == "__main__":
    main()
