import requests
from PIL import Image
from io import BytesIO
from WGS84_GCJ02 import wgs84_to_gcj02

# 高德地图API的URL
url = "https://restapi.amap.com/v3/staticmap"

# 替换为你申请的高德API密钥
api_key = "fd2adfc70d86343b1181195c0fc9a0ee"

# GNSS原始坐标（WGS-84）
gnss_lon = 112.99501037597656
gnss_lat = 28.229507446289062

# 转换为高德坐标系（GCJ-02）
center_lon, center_lat = wgs84_to_gcj02(gnss_lon, gnss_lat)

# 图片尺寸
width = 1920
height = 1080

# 缩放级别
zoom = 18

# 构建请求参数
params = {
    "location": f"{center_lon},{center_lat}",
    "zoom": zoom,
    "size": f"{width}*{height}",
    "scale": 2,  # 图片质量，2表示高清
    "maptype": "satellite",  # 卫星地图
    "markers": f"mid,0xFF0000,A:{center_lon},{center_lat}",  # 红色标记点
    "key": api_key
}

# 发送请求获取图片
response = requests.get(url, params=params)

# 检查请求是否成功
if response.status_code == 200:
    # 将图片保存到本地
    with open("map_image.png", "wb") as f:
        f.write(response.content)

    # 显示图片
    image = Image.open(BytesIO(response.content))
    image.show()
else:
    print("Failed to fetch the map image.")
